# 电商产品爬虫系统需求文档

## 1. 项目概述

### 1.1 项目背景

将现有的电商产品爬虫系统从单机版本升级为分布式客户端-服务端架构，通过分发客户端给用户来采集商品数据，并建立佣金激励机制。

### 1.2 项目目标

- 构建稳定可扩展的服务端系统
- 实现用户管理和任务分发机制
- 建立佣金计算和结算体系
- 提供完整的数据采集和管理功能

### 1.3 技术栈

- **后端**: FastAPI + SQLAlchemy + PostgreSQL/SQLite
- **认证**: JWT Token
- **部署**: Docker + Nginx
- **现有爬虫**: 天猫/京东 API (已实现)

## 2. 功能需求

### 2.1 用户管理系统

#### 2.1.1 用户注册 (邀请机制)

**功能描述**: 通过邀请码注册新用户

- **输入**: 邀请码、用户名、密码、邮箱/手机号
- **验证**: 邀请码有效性、用户名唯一性
- **输出**: 用户账号创建成功，返回用户 ID
- **业务规则**:
  - 邀请码一次性使用
  - 邀请码有有效期限制
  - 新用户默认为普通用户等级

#### 2.1.2 用户登录

**功能描述**: 用户身份验证和 Token 颁发

- **输入**: 用户名/邮箱、密码
- **验证**: 账号密码正确性、账号状态
- **输出**: JWT Token、用户基本信息
- **业务规则**:
  - Token 有效期 24 小时
  - 支持 Token 刷新机制
  - 记录登录日志

#### 2.1.3 用户信息管理

**功能描述**: 用户资料查看和修改

- **查看**: 用户基本信息、等级、佣金余额
- **修改**: 密码、邮箱、手机号
- **权限**: 用户只能修改自己的信息

### 2.2 任务管理系统

#### 2.2.1 获取任务

**功能描述**: 客户端从服务端拉取待执行任务

- **输入**: 用户 Token、平台类型(tmall/jd)、任务类型
- **输出**: 任务详情(商品 ID、任务 ID、佣金金额)
- **业务规则**:
  - 根据用户等级分配不同优先级任务
  - 同一用户同时最多 3 个未完成任务
  - 任务有超时机制(30 分钟)

#### 2.2.2 任务分配策略

**功能描述**: 智能任务分配算法

- **优先级**: 用户等级 > 历史成功率 > 注册时间
- **负载均衡**: 避免单用户任务过载
- **平台分配**: 根据用户 Cookie 可用性分配

#### 2.2.3 任务状态管理

**任务状态流转**:

```
待分配 → 已分配 → 执行中 → 已完成/失败/超时
```

### 2.3 Cookie 管理系统

#### 2.3.1 Cookie 上报

**功能描述**: 用户上报可用的 Cookie 信息

- **输入**: 平台类型、Cookie 数据、有效性验证结果
- **验证**: Cookie 格式正确性、必要字段完整性
- **存储**: 加密存储 Cookie 信息
- **业务规则**:
  - Cookie 定期验证有效性
  - 无效 Cookie 自动标记删除
  - 用户可管理自己的 Cookie

#### 2.3.2 Cookie 有效性检测

**功能描述**: 定期检测 Cookie 可用性

- **检测频率**: 每小时检测一次
- **检测方式**: 调用平台 API 验证
- **失效处理**: 通知用户更新 Cookie

### 2.4 数据采集系统

#### 2.4.1 爬取结果上报

**功能描述**: 客户端上报爬取结果

- **输入**: 任务 ID、爬取结果数据、执行状态
- **验证**: 数据完整性、格式正确性
- **存储**: 结构化存储商品数据
- **业务规则**:
  - 重复数据去重处理
  - 数据质量评分机制
  - 异常数据标记审核

#### 2.4.2 数据质量控制

**功能描述**: 确保采集数据质量

- **质量指标**: 数据完整性、准确性、时效性
- **评分机制**: 根据质量给用户评分
- **奖惩机制**: 高质量数据额外奖励

### 2.5 佣金系统

#### 2.5.1 佣金计算

**功能描述**: 根据任务完成情况计算佣金

- **计算规则**:
  - 基础佣金: 按任务类型固定金额
  - 质量奖励: 根据数据质量额外奖励
  - 等级加成: 高等级用户佣金加成
- **实时计算**: 任务完成后立即计算佣金

#### 2.5.2 佣金结算

**功能描述**: 定期结算用户佣金

- **结算周期**: 每周结算一次
- **结算条件**: 最低结算金额 10 元
- **结算方式**: 暂不集成支付系统，记录结算记录
- **业务规则**:
  - 结算前 7 天数据不可修改
  - 异常数据扣除相应佣金

#### 2.5.3 佣金查询

**功能描述**: 用户查询佣金明细

- **查询内容**: 总佣金、可结算佣金、历史结算记录
- **明细展示**: 按任务展示佣金明细
- **统计报表**: 按时间维度统计佣金

## 3. 非功能需求

### 3.1 性能需求

- **并发用户**: 支持 1000+并发用户
- **响应时间**: API 响应时间 < 500ms
- **任务处理**: 每分钟处理 1000+任务请求
- **数据存储**: 支持千万级数据存储

### 3.2 安全需求

- **数据加密**: 敏感数据(Cookie、密码)加密存储
- **访问控制**: 基于 JWT 的身份认证
- **API 安全**: 接口限流、防重放攻击
- **数据备份**: 定期数据备份机制

### 3.3 可用性需求

- **系统可用性**: 99.5%以上
- **故障恢复**: 故障后 5 分钟内恢复
- **监控告警**: 完整的系统监控和告警

### 3.4 扩展性需求

- **水平扩展**: 支持多实例部署
- **数据库扩展**: 支持读写分离
- **缓存机制**: Redis 缓存热点数据

## 4. 约束条件

### 4.1 技术约束

- 使用现有的爬虫 API(天猫/京东)
- 不集成第三方支付系统
- 基于 Python 技术栈开发

### 4.2 业务约束

- 邀请制注册，控制用户增长
- Cookie 由用户自行提供和维护
- 佣金结算暂不自动化

### 4.3 合规约束

- 遵守相关法律法规
- 保护用户隐私数据
- 合理使用爬虫技术

## 5. 验收标准

### 5.1 功能验收

- [ ] 用户注册登录功能正常
- [ ] 任务分配和执行流程完整
- [ ] Cookie 管理功能可用
- [ ] 佣金计算准确无误
- [ ] 数据采集和存储正常

### 5.2 性能验收

- [ ] 支持预期并发用户数
- [ ] API 响应时间满足要求
- [ ] 系统稳定运行 24 小时以上

### 5.3 安全验收

- [ ] 通过安全测试
- [ ] 敏感数据加密存储
- [ ] 访问控制机制有效

## 6. 项目里程碑

### 阶段一: 基础架构 (1-2 周)

- 项目架构搭建
- 数据库设计和创建
- 基础 API 框架

### 阶段二: 核心功能 (2-3 周)

- 用户管理系统
- 任务管理系统
- Cookie 管理系统

### 阶段三: 业务功能 (2-3 周)

- 数据采集系统
- 佣金计算系统
- 系统集成测试

### 阶段四: 优化部署 (1 周)

- 性能优化
- 安全加固
- 生产环境部署
