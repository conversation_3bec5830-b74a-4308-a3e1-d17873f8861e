# 爬虫系统服务端需求

## 核心功能

### 1. 用户系统

- **注册**: 邀请码注册
- **登录**: 用户名密码登录，返回 JWT token
- **用户信息**: 查看个人信息和佣金余额

### 2. 任务系统

- **获取任务**: 客户端拉取爬虫任务(商品 ID)
- **上报结果**: 客户端提交爬取的商品数据

### 3. Cookie 管理

- **上报 Cookie**: 用户提交可用的 Cookie
- **Cookie 验证**: 定期检查 Cookie 有效性

### 4. 佣金系统

- **计算佣金**: 任务完成后自动计算佣金
- **查询佣金**: 用户查看佣金明细
- **结算佣金**: 记录结算(不集成支付)

### 5. 推荐返佣系统 (二级分销)

- **推荐注册**: 用户生成推荐码，邀请新用户注册
- **推荐关系**: 记录推荐人和被推荐人的关系链
- **返佣计算**:
  - 一级返佣: 直推用户完成任务，推荐人获得佣金分成
  - 二级返佣: 间推用户完成任务，推荐人获得少量佣金分成
- **团队统计**: 查看推荐团队的业绩和收益

## 技术选型

- FastAPI + SQLAlchemy + SQLite
- JWT 认证
- 复用现有爬虫 API(天猫/京东)

## API 接口

### 用户接口

- `POST /register` - 注册用户
- `POST /login` - 用户登录
- `GET /user/info` - 获取用户信息

### 任务接口

- `GET /tasks/pull` - 拉取任务
- `POST /tasks/submit` - 提交结果

### Cookie 接口

- `POST /cookies/upload` - 上报 Cookie
- `GET /cookies/status` - Cookie 状态

### 佣金接口

- `GET /commission/balance` - 查询余额
- `GET /commission/history` - 佣金明细

### 推荐返佣接口

- `GET /referral/code` - 获取推荐码
- `GET /referral/team` - 查看推荐团队
- `GET /referral/commission` - 返佣收益明细

## 返佣规则

### 推荐关系

- 用户注册时填写推荐码，建立推荐关系
- 推荐关系最多二级: 推荐人 → 直推用户 → 间推用户
- 推荐关系一旦建立不可更改

### 返佣比例 (示例)

- **一级返佣**: 直推用户任务佣金的 10%
- **二级返佣**: 间推用户任务佣金的 5%
- **返佣上限**: 单个任务返佣不超过任务佣金的 15%

### 合规说明

- 严格控制二级分销，不超过三级
- 返佣基于实际任务完成，非拉人头
- 遵守相关法律法规

## 管理端接口

### 管理员认证

- `POST /admin/login` - 管理员登录
- `POST /admin/logout` - 管理员登出

### 用户管理

- `GET /admin/users` - 用户列表(分页、搜索、筛选)
- `GET /admin/users/{user_id}` - 用户详情
- `PUT /admin/users/{user_id}/status` - 修改用户状态(启用/禁用)
- `POST /admin/invites` - 生成邀请码
- `GET /admin/invites` - 邀请码列表

### 任务管理

- `POST /admin/tasks` - 创建任务(批量添加商品 ID)
- `GET /admin/tasks` - 任务列表(状态筛选、分页)
- `PUT /admin/tasks/{task_id}` - 修改任务状态
- `DELETE /admin/tasks/{task_id}` - 删除任务
- `GET /admin/tasks/stats` - 任务统计数据

### Cookie 管理

- `GET /admin/cookies` - Cookie 列表(按用户、平台筛选)
- `PUT /admin/cookies/{cookie_id}/status` - 修改 Cookie 状态
- `DELETE /admin/cookies/{cookie_id}` - 删除无效 Cookie

### 佣金管理

- `GET /admin/commissions` - 佣金记录列表
- `PUT /admin/commissions/{commission_id}` - 修改佣金状态
- `POST /admin/settlements` - 创建结算批次
- `GET /admin/settlements` - 结算记录列表

### 推荐管理

- `GET /admin/referrals` - 推荐关系树
- `GET /admin/referrals/stats` - 推荐统计数据
- `PUT /admin/referrals/{user_id}/commission_rate` - 调整用户返佣比例

### 系统管理

- `GET /admin/stats/dashboard` - 系统概览数据
- `GET /admin/logs` - 系统日志
- `PUT /admin/settings` - 系统配置(返佣比例、任务佣金等)
