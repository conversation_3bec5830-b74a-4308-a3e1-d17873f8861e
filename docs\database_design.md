# 数据库设计文档

## 表结构设计

### 1. 用户表 (users)

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    status ENUM('active', 'disabled') DEFAULT 'active',
    referrer_id INTEGER,  -- 推荐人ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id)
);
```

### 2. 管理员表 (admins)

```sql
CREATE TABLE admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'super_admin') DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 平台表 (platforms)

```sql
CREATE TABLE platforms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,  -- 平台代码(tmall, jd, pdd等)
    name VARCHAR(100) NOT NULL,  -- 平台名称
    base_price DECIMAL(10,2) NOT NULL,  -- 任务标定价格
    commission DECIMAL(10,2) NOT NULL,  -- 任务佣金
    status ENUM('active', 'disabled') DEFAULT 'active',
    description TEXT,  -- 平台描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. 邀请码表 (invite_codes)

```sql
CREATE TABLE invite_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(32) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,  -- 生成邀请码的用户
    used_by INTEGER,  -- 使用邀请码的用户
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (used_by) REFERENCES users(id)
);
```

### 5. 任务表 (tasks)

```sql
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id VARCHAR(100) NOT NULL,  -- 商品ID
    platform_id INTEGER NOT NULL,  -- 平台ID
    task_type VARCHAR(50) DEFAULT 'BasicInfo',
    status ENUM('pending', 'assigned', 'in_progress', 'completed', 'failed', 'timeout') DEFAULT 'pending',
    assigned_to INTEGER,  -- 分配给的用户
    assigned_at TIMESTAMP,
    completed_at TIMESTAMP,
    import_batch_id VARCHAR(50),  -- 批量导入批次ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (platform_id) REFERENCES platforms(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id)
);
```

### 6. 任务导入记录表 (task_imports)

```sql
CREATE TABLE task_imports (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id VARCHAR(50) UNIQUE NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_type ENUM('excel', 'csv', 'json') NOT NULL,
    total_count INTEGER NOT NULL,  -- 总任务数
    success_count INTEGER DEFAULT 0,  -- 成功导入数
    failed_count INTEGER DEFAULT 0,  -- 失败数
    status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
    error_details JSON,  -- 错误详情
    created_by INTEGER NOT NULL,  -- 操作管理员
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admins(id)
);
```

### 7. Cookie 表 (cookies)

```sql
CREATE TABLE cookies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    platform_id INTEGER NOT NULL,  -- 平台ID
    cookie_data TEXT NOT NULL,  -- 加密存储的Cookie数据
    status ENUM('valid', 'invalid', 'unknown') DEFAULT 'unknown',
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (platform_id) REFERENCES platforms(id)
);
```

### 7. 任务结果表 (task_results)

```sql
CREATE TABLE task_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    result_data JSON,  -- 爬取的商品数据
    success BOOLEAN NOT NULL,
    error_message TEXT,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 8. 佣金记录表 (commissions)

```sql
CREATE TABLE commissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    task_id INTEGER NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    type ENUM('task', 'referral') NOT NULL,  -- 任务佣金或推荐奖励
    status ENUM('pending', 'confirmed', 'settled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (task_id) REFERENCES tasks(id)
);
```

### 8. 结算记录表 (settlements)

```sql
CREATE TABLE settlements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    commission_ids JSON,  -- 包含的佣金记录ID列表
    status ENUM('pending', 'completed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 9. 系统配置表 (system_settings)

```sql
CREATE TABLE system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 索引设计

```sql
-- 用户表索引
CREATE INDEX idx_users_referrer ON users(referrer_id);
CREATE INDEX idx_users_status ON users(status);

-- 邀请码表索引
CREATE INDEX idx_invite_codes_user ON invite_codes(user_id);
CREATE INDEX idx_invite_codes_code ON invite_codes(code);

-- 平台表索引
CREATE INDEX idx_platforms_code ON platforms(code);
CREATE INDEX idx_platforms_status ON platforms(status);

-- 任务表索引
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_assigned ON tasks(assigned_to);
CREATE INDEX idx_tasks_platform ON tasks(platform_id);
CREATE INDEX idx_tasks_created ON tasks(created_at);

-- Cookie表索引
CREATE INDEX idx_cookies_user_platform ON cookies(user_id, platform_id);
CREATE INDEX idx_cookies_status ON cookies(status);

-- 佣金表索引
CREATE INDEX idx_commissions_user ON commissions(user_id);
CREATE INDEX idx_commissions_task ON commissions(task_id);
CREATE INDEX idx_commissions_status ON commissions(status);
CREATE INDEX idx_commissions_type ON commissions(type);

-- 结算表索引
CREATE INDEX idx_settlements_user ON settlements(user_id);
CREATE INDEX idx_settlements_status ON settlements(status);
```

## 初始数据

```sql
-- 平台初始数据
INSERT INTO platforms (code, name, base_price, commission) VALUES
('tb', '淘宝', 1.50, 0.80,),
('jd', '京东', 1.00, 0.60,),
('pdd', '拼多多', 0.80, 0.40);

-- 系统配置初始数据
INSERT INTO system_settings (key, value, description) VALUES
('referral_commission_rate', '0.10', '推荐佣金比例'),
('min_settlement_amount', '10.00', '最低结算金额'),
('task_timeout_minutes', '30', '任务超时时间(分钟)'),
('max_active_tasks_per_user', '3', '用户最大同时任务数');

-- 默认管理员账号
INSERT INTO admins (username, password_hash, role) VALUES
('admin', '$2b$12$...', 'super_admin');  -- 密码需要实际加密
```

## 关系说明

1. **用户推荐关系**: users.referrer_id → users.id (自关联)
2. **邀请码关系**: invite_codes.user_id → users.id, invite_codes.used_by → users.id
3. **平台关系**: tasks.platform_id → platforms.id, cookies.platform_id → platforms.id
4. **任务分配**: tasks.assigned_to → users.id
5. **Cookie 归属**: cookies.user_id → users.id
6. **佣金归属**: commissions.user_id → users.id, commissions.task_id → tasks.id
7. **结算关系**: settlements.user_id → users.id

## 数据完整性约束

1. 用户不能推荐自己 (referrer_id ≠ id)
2. 邀请码只能使用一次 (used_by 唯一性)
3. 任务只能分配给一个用户
4. 佣金记录不可删除，只能修改状态
5. 结算完成后不可修改
