import json
import time
import hashlib
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from curl_cffi import requests
from DrissionPage import WebPage
import random
import re

def get_item_info(item_id, cookies):
    """
    ## 获取天猫商品详情
    - `item_id`: 商品id
    - `cookies`: dict, 必须包含 _m_h5_tk
    Returns:
        dict: {success: bool, data: dict|None, msg: str}
    """

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
        "referer": "https://item.taobao.com/",
    }
    if not item_id or not isinstance(cookies, dict):
        return {
            "success": False,
            "data": None,
            "msg": "参数不能为空，或 cookies 格式不正确",
        }
    try:
        # 原 _update_cookies 逻辑
        time_stamp = str(int(time.time() * 1000))
        params_dict = {
            "resId": "33799945",
            "bizId": "443",
            "fromUrl": "https://www.taobao.com/",
        }
        data_info_dict = {"params": json.dumps(params_dict, ensure_ascii=False)}
        data_info = json.dumps(data_info_dict, ensure_ascii=False)
        token = "undefined"
        update_url = "https://h5api.m.taobao.com/h5/mtop.tmall.kangaroo.core.service.route.aldlampservicefixedresv2/1.0/"
        update_params = {
            "appKey": "12574478",
            "t": time_stamp,
            "sign": hashlib.md5(
                (token + "&" + time_stamp + "&" + "12574478" + "&" + data_info).encode()
            ).hexdigest(),
            "data": data_info,
        }
        update_response = requests.get(
            update_url, headers=headers, params=update_params
        )
        cookie = update_response.cookies.get_dict()  # _m_h5_tk  _m_h5_tk_enc
        cookies.update(cookie)
    except Exception as e:
        return {
            "success": False,
            "data": None,
            "msg": f"更新 cookies 时发生错误: {str(e)}",
        }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
        "referer": "https://item.taobao.com/",
    }
    if not item_id or not isinstance(cookies, dict):
        return {
            "success": False,
            "data": None,
            "msg": "参数不能为空，或 cookies 格式不正确",
        }
    try:
        url = "https://h5api.m.taobao.com/h5/mtop.taobao.pcdetail.data.get/1.0/"
        time_stamp = str(int(time.time() * 1000))
        exParams = {
            "spm": "a21bo.jianhua/a.201876.d37.5af92a89I61ERe",
            "id": str(item_id),
            "scm": "1007.40986.369799.0",
            "pvid": "232c8d2b-d645-45d6-b9ad-60e321fbf8bc",
            "queryParams": f"id={item_id}&pvid=232c8d2b-d645-45d6-b9ad-60e321fbf8bc&scm=1007.40986.369799.0&spm=a21bo.jianhua%2Fa.201876.d37.5af92a89I61ERe",
            "domain": "https://item.taobao.com",
            "path_name": "/item.htm",
        }
        data_info_dict = {
            "id": str(item_id),
            "detail_v": "3.3.2",
            "exParams": json.dumps(exParams, ensure_ascii=False),
        }
        data_info = json.dumps(data_info_dict, ensure_ascii=False)
        token = cookies.get("_m_h5_tk", "").split("_")[0]
        params = {
            "appKey": "12574478",
            "t": time_stamp,
            "sign": hashlib.md5(
                (token + "&" + time_stamp + "&" + "12574478" + "&" + data_info).encode()
            ).hexdigest(),
            "ttid": "2022@taobao_litepc_9.17.0",
            "data": data_info,
        }
        response = requests.get(
            url, headers=headers, cookies=cookies, params=params, timeout=10
        )
        if response.status_code != 200:
            return {
                "success": False,
                "data": None,
                "msg": f"HTTP请求失败，状态码: {response.status_code}",
            }
        try:
            data = response.json()
        except Exception as e:
            return {
                "success": False,
                "data": None,
                "msg": f"响应JSON解析失败: {str(e)}",
            }
        if not data or "data" not in data:
            return {"success": False, "data": None, "msg": "未获取到商品信息"}
        return {"success": True, "data": data["data"], "msg": "获取商品信息成功"}
    except Exception as e:
        return {"success": False, "data": None, "msg": f"未知错误: {str(e)}"}

def login_by_browser(cookies):
    page = WebPage()
    page.get("https://www.taobao.com/favicon.ico")
    cookies.update({"domain": ".taobao.com"})
    page.set.cookies(cookies)
    return page

def get_item_info_by_browser(item_id,page):
    page.get("https://taobao.com")
    items_list = page.eles("tag=a@@href://item.taobao.com/item.htm?")
    items_links = [item.attr("href") for item in items_list]
    link = random.choice(items_links)
    url_obj = urlparse(link)
    query = parse_qs(url_obj.query)
    query["id"] = [str(item_id)]
    new_query = urlencode(query, doseq=True)
    new_link = urlunparse(
        (
            url_obj.scheme,
            url_obj.netloc,
            url_obj.path,
            url_obj.params,
            new_query,
            url_obj.fragment,
        )
    )
    page.listen.start("h5/mtop.taobao.pcdetail.data.get/1.0/")
    page.get(new_link)
    datapacket = page.listen.wait()
    body = datapacket.response.body
    # 提取 mtopjsonppcdetail2({ ... }) 中的 {...}
   
    match = re.search(r'mtopjsonppcdetail2\((\{.*\})\)', body)
    if match:
        json_str = match.group(1)
        try:
            data = json.loads(json_str)
        except Exception as e:
            return {"success": False, "data": None, "msg": f"JSON解析失败: {e}"}

        return {
            "success": True,
            "data": data,
            "msg": "获取商品信息成功",
        }
    else:
        return {"success": False, "data": None, "msg": "未匹配到商品信息JSON"}


# 示例用法
if __name__ == "__main__":
    cookies = {
        "thw": "cn",
        "cna": "YcOIIIeqPWYCAToXMMm7P8r/",
        "t": "85704b7f036d7c5de1950b2956415f14",
        "lgc": "%5Cu6211%5Cu662F%5Cu7A00%5Cu996D%5Cu554A233",
        "dnk": "%5Cu6211%5Cu662F%5Cu7A00%5Cu996D%5Cu554A233",
        "tracknick": "%5Cu6211%5Cu662F%5Cu7A00%5Cu996D%5Cu554A233",
        "_hvn_lgc_": "0",
        "wk_cookie2": "1083212944428c2c1d8d3a4cdc61434f",
        "wk_unb": "UUpic6C3uVr33g%3D%3D",
        "aui": "2231823387",
        "_samesite_flag_": "true",
        "cookie2": "141317523c794e85219ba5853c7a60da",
        "_tb_token_": "e6ee77ae35015",
        "sca": "076e4a0e",
        "havana_sdkSilent": "1753520052934",
        "arms_uid": "d52683f9-6fdf-4023-ae62-0713cdc2bb2c",
        "xlly_s": "1",
        "sn": "",
        "cancelledSubSites": "empty",
        "cookie3_bak": "141317523c794e85219ba5853c7a60da",
        "cookie3_bak_exp": "1753797394665",
        "env_bak": "FM%2BgzJsXgQz0PXux%2FKJM0hTZUHIFZwdHg6es4ZMOEeJj",
        "sdkSilent": "1753624594671",
        "3PcFlag": "1753552050915",
        "uc3": "id2=UUpic6C3uVr33g%3D%3D&vt3=F8dD2fnoLYjKMw5HhqI%3D&nk2=rUtEofhoWTH%2FBpxUSA%3D%3D&lg2=V32FPkk%2Fw0dUvg%3D%3D",
        "csg": "3a8fe785",
        "skt": "8d5be23c5bccc19b",
        "existShop": "MTc1MzU1MjA4Mw%3D%3D",
        "uc4": "nk4=0%40r7rCNZIMfIZqaZc6LJVPcYphpmwN%2FPMu&id4=0%40U2gotiA7wqlVhdRGTdJXMvomAtXI",
        "_cc_": "VFC%2FuZ9ajQ%3D%3D",
        "sgcookie": "E100epRVcSOf1QLZ8ig2hoPmZVVs%2FPrY9tSj%2FKOYDO%2B%2BKUCX1An2ncTptrBQv4PJtN8UblXCA6P%2FYZVizznwddD43blCOv5XuBsl5%2F%2BeWVEo2VBDCxz2lrrc5j%2F5fY%2BxQssw",
        "havana_lgc2_0": "eyJoaWQiOjIyMzE4MjMzODcsInNnIjoiZWIyMzYwMGVhMDhkMGZmNWI1NGNlM2EzY2U1OWQ3MWEiLCJzaXRlIjowLCJ0b2tlbiI6IjFRNHVmWEF5WjZWUDhqbW8ySTZzMXJBIn0",
        "useNativeIM": "false",
        "havana_lgc_exp": "1784656195000",
        "isg": "BNbWOxIUoiR8PpYEpf7EUjK3J4zYdxqxDS2ddUA947lkA3KdqAaKwf67m99KqxLJ",
        "mtop_partitioned_detect": "1",
        "_m_h5_tk": "9ded0c866ccbc4072f29d5080b355a72_1753614561369",
        "_m_h5_tk_enc": "809ca84a01ca3feefc5e9fd48863e0a8",
        "tfstk": "gn-jKfbmSSVbv5sTCZDzAFXYUgj_fYoEfR69KdE4BiIxfOdeNNEZoi71fQCy0sJVDc69TQ_9_1J4nUAepo-qoGr1mGjtTXoEY-vcjGLLB5lai8BcHczTX1Ic2SHUz8nEYKvY3tHeeD-aji_PN1IOksQ-2_XgM1dOk7CRZOXTMOExFY1lQPeAX1ER29XLkGI96LH5I_COXZd9evFEVOHfgKMI0K_CdS_JH_ZTXnnGl6Ob7lrwqtQXXKCWuE-5hZ1dWboPendyBn-crXNdjL8B1FdqZlC2RLIAaEh8x_LhMi-cjRUAz3J9bgprMo1XyIAOPEcb0sQ60F_2-Xah_NB1JGQ7BlpW3sOOFMFbAa-yBhSJUjEfGUdDXi-KLPXlWU-c-nl8cMLH3i89gbqOge9RVg70Y69udPw5-l65TYM7SPvjG7-Uu95dLZBlhWkSFSeGkTX5TYM7SPbAEtMEFYNYI",
    }
    item_id = "731002270762"
    page = login_by_browser(cookies=cookies)
    result = get_item_info_by_browser(item_id=item_id,page=page)
    
