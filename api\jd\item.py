from curl_cffi import requests
import re
import json5


def get_item_info(item_id,cookies):
    """
    ## 获取京东商品详情
    - `item_id`: 商品id
    - `cookies`: dict, 必须包含 'shshshfpx' 和 'pt_key'
    
    Returns:
        dict: {success: bool, data: dict|None, msg: str}
    """
    # 卫语句：参数验证
    if not item_id or not isinstance(cookies, dict) or not cookies.get('shshshfpx') or not cookies.get('pt_key'):
        return {"success": False, "data": None, "msg": "参数不能为空，或 cookies 格式不正确"}
    
    try:
        response = requests.get(
            f"https://item.m.jd.com/product/{item_id}.html", 
            cookies=cookies,
            timeout=10
        )
        
        # 卫语句：HTTP状态检查
        if response.status_code != 200:
            return {"success": False, "data": None, "msg": f"HTTP请求失败，状态码: {response.success_code}"}
        
        html_content = response.text
        
        # 卫语句：响应内容检查
        if not html_content:
            return {"success": False, "data": None, "msg": "响应内容为空"}

        item_only_pattern = r"window\._itemOnly\s*=\s*\((\{.*?\})\);"
        item_only_match = re.search(item_only_pattern, html_content, re.DOTALL)

        item_info_pattern = r"window\._itemInfo\s*=\s*\((\{.*?\})\);"
        item_info_match = re.search(item_info_pattern, html_content, re.DOTALL)
        
        item_info, item_only = None, None
        
        # 解析 itemInfo
        if item_info_match:
            try:
                item_info = json5.loads(item_info_match.group(1))
            except (json5.JSONError, ValueError) as e:
                return {"success": False, "data": None, "msg": f"itemInfo JSON解析失败: {str(e)}"}

        # 解析 itemOnly
        if item_only_match:
            try:
                item_only = json5.loads(item_only_match.group(1))
            except (json5.JSONError, ValueError) as e:
                return {"success": False, "data": None, "msg": f"itemOnly JSON解析失败: {str(e)}"}

        # 卫语句：数据完整性检查
        if not item_only and not item_info:
            return {"success": False, "data": None, "msg": "未找到商品信息数据"}
        
        if not item_only:
            return {"success": False, "data": None, "msg": "未找到itemOnly数据"}
            
        if not item_info:
            return {"success": False, "data": None, "msg": "未找到itemInfo数据"}

        return {
            "success": True, 
            "data": {"itemOnly": item_only, "itemInfo": item_info}, 
            "msg": "获取商品信息成功"
        }
        
    except Exception as e:
        return {"success": False, "data": None, "msg": f"未知错误: {str(e)}"}


if __name__ == "__main__":
    cookies = {
        "shshshfpx": "47a6a890-c7bf-eae7-c848-dcd34b695bb8-1752866841",
        "pt_key": "AAJoerdLADCg4b9N0-P3KBUS0sh2VtLKkIUG6J_mHa4_rxhOKgoobORzxJUlmyVbrmwOdC3WDyg"
    }
    item_id = "10098008823953"
    result = get_item_info(item_id, cookies)

